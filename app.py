import streamlit as st
import pandas as pd
from openpyxl import load_workbook
import os
import io
import shutil

# Constants
EXCEL_PATH = os.path.join(os.path.dirname(__file__), 'Inventory.xlsx')
SKIP_SHEET = 'SUMMARY STOCK'
TEMP_TEMPLATE_COPY = os.path.join(os.path.dirname(__file__), 'Inventory_Template.xlsx')

@st.cache_data
def load_workbook_data(path):
    """Load workbook data and handle unnamed columns better."""
    wb = load_workbook(path)
    sheet_map = {s.strip(): s for s in wb.sheetnames if s.strip() != SKIP_SHEET}
    sheet_info = {}
    
    for disp_name, actual_name in sheet_map.items():
        ws = wb[actual_name]
        # Get all headers from the first row
        raw_headers = [cell.value for cell in next(ws.iter_rows(min_row=1, max_row=1))]
        
        # Process headers to handle unnamed columns
        headers = []
        for idx, h in enumerate(raw_headers):
            if h is None or str(h).startswith('Unnamed'):
                headers.append(f"Column_{idx}")
            else:
                headers.append(str(h).strip())
        
        sheet_info[disp_name] = {
            'actual_name': actual_name,
            'headers': headers
        }
    return sheet_info

@st.cache_data
def load_sheet_df(path, disp, info):
    actual = info[disp]['actual_name']
    return pd.read_excel(path, sheet_name=actual)

@st.cache_data
def save_dataframe_to_excel(path, disp, df, info):
    actual = info[disp]['actual_name']
    try:
        with pd.ExcelWriter(path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            df.to_excel(writer, sheet_name=actual, index=False)
    except PermissionError:
        st.error(f"Unable to write to '{path}'. Please close it if open and retry.")

@st.cache_data
def update_summary(path, parts):
    rows = []
    for disp in parts:
        df = pd.read_excel(path, sheet_name=disp)
        if 'Field' in df.columns and 'Stock' in df.columns:
            rows.append({'PART NAME': disp, 'Field Count': int(df['Field'].sum()), 'Stock Count': int(df['Stock'].sum())})
    summary_df = pd.DataFrame(rows)
    with pd.ExcelWriter(path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        summary_df.to_excel(writer, sheet_name='SUMMARY', index=False)
    return summary_df

@st.cache_data
def generate_template_bytes(info):
    """Generate a template Excel file (copying first two rows to preserve merged cells)."""
    wb = load_workbook(EXCEL_PATH)
    
    # Create a new in-memory workbook for the template
    temp_wb = load_workbook(EXCEL_PATH)  # We'll remove data but keep formatting

    # Remove all existing sheets, then recreate with just headers and merged cells
    for sheet_name in list(temp_wb.sheetnames):
        if sheet_name != SKIP_SHEET:
            temp_wb.remove(temp_wb[sheet_name])
    
    # Recreate each sheet with first two rows (including merged cells)
    original_wb = load_workbook(EXCEL_PATH)
    for sheet_name in original_wb.sheetnames:
        if sheet_name == SKIP_SHEET:
            continue
        orig_ws = original_wb[sheet_name]
        new_ws = temp_wb.create_sheet(sheet_name)
        
        # Copy first two rows (to preserve merged headers and sub-columns)
        for row_idx in [1, 2]:
            for cell in orig_ws[row_idx]:
                new_cell = new_ws.cell(row=row_idx, column=cell.column, value=cell.value)
                if cell.has_style:
                    new_cell._style = cell._style
        
        # Recreate merged cell ranges in those first two rows
        for m in orig_ws.merged_cells.ranges:
            if m.min_row <= 2:
                new_ws.merge_cells(
                    start_row=m.min_row,
                    start_column=m.min_col,
                    end_row=m.max_row,
                    end_column=m.max_col
                )

    # Save to a temporary file and return its bytes
    temp_wb.save(TEMP_TEMPLATE_COPY)
    with open(TEMP_TEMPLATE_COPY, 'rb') as f:
        return f.read()

def validate_and_merge_upload(uploaded_bytes, info, actual_sheet):
    """Validate uploaded Excel file and merge with existing data if valid."""
    template_headers = info[actual_sheet]['headers']
    
    try:
        wb_up = load_workbook(io.BytesIO(uploaded_bytes))
    except Exception:
        return False, "Uploaded file is not a valid Excel workbook."

    if actual_sheet not in wb_up.sheetnames:
        return False, f"Sheet '{actual_sheet}' is missing in the upload."

    try:
        df_up = pd.read_excel(io.BytesIO(uploaded_bytes), sheet_name=actual_sheet)
        
        # Rename columns to match template headers (slice by length)
        df_up.columns = template_headers[: len(df_up.columns)]
        
        # Validate required columns have at least one non-null value
        required_cols = [col for col in template_headers if not col.startswith('Column_')]
        for col in required_cols:
            if col in df_up.columns and df_up[col].isna().all():
                return False, f"Required column '{col}' has no values."
        
        # Load existing data and rename columns similarly
        current_df = pd.read_excel(EXCEL_PATH, sheet_name=actual_sheet)
        current_df.columns = template_headers[: len(current_df.columns)]
        
        # Append new rows
        merged = pd.concat([current_df, df_up], ignore_index=True)
        
        # Save back to the same sheet
        with pd.ExcelWriter(EXCEL_PATH, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            merged.to_excel(writer, sheet_name=actual_sheet, index=False)
        
        return True, None
        
    except Exception as e:
        return False, f"Error processing upload: {str(e)}"

# --- Streamlit App ---
st.set_page_config(layout='wide')
st.title('Inventory Management: Mode 1 - Data Entry')

info = load_workbook_data(EXCEL_PATH)
all_parts = [k for k in info if k != 'SEMS DETAILS']  # List of all part sheets
entry_mode = st.radio('Select entry format', ['Form', 'Bulk Upload'], horizontal=True)

if entry_mode == 'Form':
    st.header("Form Entries")
    
    # Section selection: PARTS or SEMS DETAILS
    selected_section = st.radio('Select Section', ['PARTS', 'SEMS DETAILS'], horizontal=True)
    
    # Move part selection outside the form
    disp = None
    if selected_section == 'PARTS':
        # Map the dropdown labels to the actual sheet keys
        part_map = {
            'Energy Meter':'ENERGY METER',
            'Enclosure':'ENCLOSURE',
            'SMPS':'SMPS',
            'Controller':'CONTROLLER'
        }
        disp_label = st.selectbox('Select Part', list(part_map.keys()), key='part_selector')
        disp = part_map[disp_label]
    else:
        disp = 'SEMS DETAILS'
        if disp not in [s.strip() for s in info.keys()]:
            st.error("Sheet 'SEMS DETAILS' not found in workbook.")
            st.stop()

    meta = info[disp]
    headers = meta['headers']
    df_existing = load_sheet_df(EXCEL_PATH, disp, info)
    
    # Single form per section
    with st.form(f"form_section_{selected_section}_{disp}", clear_on_submit=True):
        # Render the form fields based on selected part or SEMS DETAILS
        def render_form_fields(part_type, headers):
            inputs = {}
            
            # If we're in the PARTS section, show a Status dropdown first
            if selected_section == 'PARTS':
                inputs['Status'] = st.selectbox('Status', ['FIELD', 'STOCK'])
            
            # ENERGY METER logic
            if part_type == 'ENERGY METER':
                maker_choice = st.radio("Select Maker", ["Secure", "Elmeasure"])
                if maker_choice == "Secure":
                    inputs['Type'] = st.selectbox("Select Secure Model", ["Secure (0.5)", "Secure (0.2)"])
                else:
                    inputs['Type'] = st.selectbox("Select Elmeasure Model", ["Elmeasure (0.5)", "Elmeasure (0.2)"])

            # CONTROLLER logic
            elif part_type == 'CONTROLLER':
                inputs['Type'] = st.selectbox("Controller Type", ['WIFI', '2G', '4G'])

            # SEMS DETAILS logic
            elif part_type == 'SEMS DETAILS':
                inputs['Status'] = st.selectbox("ASC", ['ASC', 'NO'])
                
                # Handle ASC fields
                if inputs['Status'] == 'ASC':
                    col1, col2 = st.columns(2)
                    with col1:
                        inputs['ASC (Date)'] = st.date_input("ASC Date")
                    with col2:
                        inputs['ASC (Amount)'] = st.number_input("ASC Amount", min_value=0.0)
                else:
                    inputs['ASC (Date)'] = None
                    inputs['ASC (Amount)'] = None

            # Process all other fields from headers
            for hdr in headers:
                # Skip fields that have already been handled and removed fields
                if (hdr not in ['Status', 'Type', 'ASC (Date)', 'ASC (Amount)', 'ASC', 'AMOUNT', 'BALANCE'] and 
                    not hdr.startswith('Column_')):
                    if 'DATE' in hdr.upper():
                        inputs[hdr] = st.date_input(hdr)
                    elif 'SR NO' in hdr.upper():
                        help_text = f"Enter {part_type.lower()} serial number"
                        inputs[hdr] = st.text_input(hdr, help=help_text)
                    else:
                        inputs[hdr] = st.text_input(hdr)

            return inputs

        inputs = render_form_fields(disp, headers)
        inputs['Section'] = selected_section
        if selected_section == 'PARTS':
            inputs['Part Type'] = meta['actual_name']

        submitted = st.form_submit_button('Submit Entry')
        if submitted:
            # Convert any date inputs to ISO strings for Pandas
            for key, value in inputs.items():
                if isinstance(value, pd.Timestamp):
                    inputs[key] = value.strftime('%Y-%m-%d')
                elif value is None:
                    inputs[key] = ''  # Convert None to empty string for Excel
            
            # Ensure new row matches existing DataFrame columns
            df_columns = df_existing.columns
            new_row = pd.DataFrame([inputs])
            for col in df_columns:
                if col not in new_row.columns:
                    new_row[col] = ''
            new_row = new_row.reindex(columns=df_columns)
            
            new_df = pd.concat([df_existing, new_row], ignore_index=True)
            save_dataframe_to_excel(EXCEL_PATH, disp, new_df, info)
            st.success(f'Entry for {selected_section} added successfully!')
            st.dataframe(update_summary(EXCEL_PATH, all_parts))

else:
    st.header("Bulk Upload")
    section = st.radio('Section', ['PARTS', 'SEMS DETAILS'], horizontal=True)
    disp = None
    if section == 'PARTS':
        part_map = {
            'Energy Meter':'ENERGY METER',
            'Enclosure':'ENCLOSURE',
            'SMPS':'SMPS',
            'Controller':'CONTROLLER'
        }
        disp_label = st.selectbox('Select Part', list(part_map.keys()))
        disp = next(k for k,v in info.items() if v['actual_name'] == part_map[disp_label])
    else:
        disp = next((k for k in info if k.strip() == 'SEMS DETAILS'), None)
        if not disp:
            st.error("Sheet 'SEMS DETAILS' not found in workbook.")
            st.stop()

    # Template download button (preserves merged cells)
    template_bytes = generate_template_bytes(info)
    st.download_button(
        'Download Excel Template',
        data=template_bytes,
        file_name='Inventory_Template.xlsx',
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

    # File upload section
    uploaded_file = st.file_uploader("Upload filled Excel file", type=['xlsx'])
    if uploaded_file:
        success, error_msg = validate_and_merge_upload(uploaded_file.read(), info, disp)
        if success:
            st.success("Data uploaded successfully!")
            st.dataframe(update_summary(EXCEL_PATH, all_parts))
        else:
            st.error(f"Upload failed: {error_msg}")

# Dependencies:
# pip install streamlit openpyxl pandas
