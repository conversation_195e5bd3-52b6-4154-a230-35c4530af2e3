import streamlit as st
import pandas as pd
from openpyxl import load_workbook
import os
import io

# Constants
EXCEL_PATH = os.path.join(os.path.dirname(__file__), "Inventory.xlsx")
SKIP_SHEET = "SUMMARY STOCK"

# Load available sheets
@st.cache_data
def get_sheet_names(path):
    wb = load_workbook(path, read_only=True)
    return [s for s in wb.sheetnames if s.strip() != SKIP_SHEET]

# Load data from a specific sheet
@st.cache_data
def load_sheet_df(path, sheet_name):
    return pd.read_excel(path, sheet_name=sheet_name)

# Filter the dataframe by a search term
def filter_df(df: pd.DataFrame, term: str) -> pd.DataFrame:
    if not term:
        return df
    term = term.lower()
    return df[df.apply(lambda row: row.astype(str).str.lower().str.contains(term).any(), axis=1)]

# Save only the 'Status' column back to the Excel file
def update_status_column(sheet_name, new_df):
    existing_df = pd.read_excel(EXCEL_PATH, sheet_name=sheet_name)
    if 'Status' in existing_df.columns and 'Status' in new_df.columns:
        existing_df['Status'] = new_df['Status']
        with pd.ExcelWriter(EXCEL_PATH, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            existing_df.to_excel(writer, sheet_name=sheet_name, index=False)

# Streamlit UI
st.set_page_config(layout="wide")
st.title("📊 Inventory Management – Mode 2: Observation Dashboard")

# Section Selector
section = st.radio("Select Section", ["PARTS", "SEMS DETAILS", "INVENTORY SUMMARY SHEET"], horizontal=True)

if section == "PARTS":
    # List available PART sheets
    sheet_names = get_sheet_names(EXCEL_PATH)
    part_sheets = [s for s in sheet_names if s not in ["SEMS DETAILS", "SUMMARY"]]
    selected_sheet = st.selectbox("Select Part", part_sheets)

    df = load_sheet_df(EXCEL_PATH, selected_sheet)

    # Search
    search_term = st.text_input("🔍 Search this sheet", placeholder="Type to filter data...")
    filtered_df = filter_df(df, search_term)

    st.markdown(f"**Showing {len(filtered_df)} of {len(df)} rows**")

    # Allow editing only 'Status'
    if 'Status' in filtered_df.columns:
        edited_df = st.data_editor(
            filtered_df,
            column_config={col: None for col in filtered_df.columns if col != 'Status'},
            use_container_width=True,
            num_rows="dynamic"
        )

        if st.button("💾 Update Status Column"):
            update_status_column(selected_sheet, edited_df)
            st.success("✅ Status column updated successfully.")
    else:
        st.warning("No 'Status' column found. Nothing editable.")
        st.dataframe(filtered_df, use_container_width=True)

elif section == "SEMS DETAILS":
    df = load_sheet_df(EXCEL_PATH, "SEMS DETAILS")

    search_term = st.text_input("🔍 Search SEMS DETAILS", placeholder="Type to filter data...")
    filtered_df = filter_df(df, search_term)

    st.dataframe(filtered_df, use_container_width=True)

elif section == "INVENTORY SUMMARY SHEET":
    summary_parts = ["ENERGY METER", "ENCLOSURE", "SMPS", "CONTROLLER"]
    summary_data = []

    for part in summary_parts:
        try:
            df = load_sheet_df(EXCEL_PATH, part)
            field_count = df['Field'].sum() if 'Field' in df.columns else 0
            stock_count = df['Stock'].sum() if 'Stock' in df.columns else 0
            summary_data.append({
                "PART NAME": part,
                "Field Count": int(field_count),
                "Stock Count": int(stock_count)
            })
        except Exception as e:
            st.error(f"Error loading {part}: {e}")

    st.subheader("Inventory Summary")
    st.dataframe(pd.DataFrame(summary_data), use_container_width=True)
